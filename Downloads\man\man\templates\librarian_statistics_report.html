{% extends "base.html" %}

{% block title %}Statistics Report - Librarian{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_payment_management') }}">
                <i class="fas fa-credit-card me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('librarian_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-chart-line me-3 text-primary"></i>Statistics Report</h2>
        <p class="text-muted mb-0">Comprehensive library usage and performance statistics</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Date Range Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-calendar me-2"></i>Date Range</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="dateFrom" class="form-label">From Date</label>
                <input type="date" class="form-control" id="dateFrom">
            </div>
            <div class="col-md-3">
                <label for="dateTo" class="form-label">To Date</label>
                <input type="date" class="form-control" id="dateTo">
            </div>
            <div class="col-md-3">
                <label for="reportType" class="form-label">Report Type</label>
                <select class="form-select" id="reportType">
                    <option value="overview">Overview</option>
                    <option value="circulation">Circulation</option>
                    <option value="collection">Collection</option>
                    <option value="users">Users</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button class="btn btn-primary" onclick="generateReport()">
                    <i class="fas fa-chart-bar me-2"></i>Generate Report
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Circulation</h6>
                        <h3 class="mb-0" id="totalCirculation">0</h3>
                        <small id="circulationChange">+0% from last period</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exchange-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Active Members</h6>
                        <h3 class="mb-0" id="activeMembers">0</h3>
                        <small id="membersChange">+0% from last period</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Collection Size</h6>
                        <h3 class="mb-0" id="collectionSize">0</h3>
                        <small id="collectionChange">+0% from last period</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Utilization Rate</h6>
                        <h3 class="mb-0" id="utilizationRate">0%</h3>
                        <small id="utilizationChange">+0% from last period</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Circulation Trends</h5>
            </div>
            <div class="card-body">
                <canvas id="circulationChart" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Collection Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="collectionChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Department Statistics -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Department Usage</h5>
            </div>
            <div class="card-body">
                <canvas id="departmentChart" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Peak Hours</h5>
            </div>
            <div class="card-body">
                <canvas id="peakHoursChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Statistics Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Detailed Statistics</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="statisticsTable">
                <thead class="table-dark">
                    <tr>
                        <th>Metric</th>
                        <th>Current Period</th>
                        <th>Previous Period</th>
                        <th>Change</th>
                        <th>Trend</th>
                    </tr>
                </thead>
                <tbody id="statisticsTableBody">
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let circulationChart, collectionChart, departmentChart, peakHoursChart;

// Initialize the page
$(document).ready(function() {
    // Set default dates (last 30 days)
    const today = new Date();
    const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(lastMonth.toISOString().split('T')[0]);
    
    generateReport();
});

function generateReport() {
    loadStatistics();
    loadCharts();
    loadDetailedTable();
}

function loadStatistics() {
    // Simulate API call with mock data
    const mockStats = {
        totalCirculation: 1250,
        circulationChange: '+15%',
        activeMembers: 450,
        membersChange: '+8%',
        collectionSize: 15000,
        collectionChange: '+3%',
        utilizationRate: 75,
        utilizationChange: '+5%'
    };
    
    $('#totalCirculation').text(mockStats.totalCirculation);
    $('#circulationChange').text(mockStats.circulationChange + ' from last period');
    $('#activeMembers').text(mockStats.activeMembers);
    $('#membersChange').text(mockStats.membersChange + ' from last period');
    $('#collectionSize').text(mockStats.collectionSize);
    $('#collectionChange').text(mockStats.collectionChange + ' from last period');
    $('#utilizationRate').text(mockStats.utilizationRate + '%');
    $('#utilizationChange').text(mockStats.utilizationChange + ' from last period');
}

function loadCharts() {
    // Circulation Trends Chart
    const circulationCtx = document.getElementById('circulationChart').getContext('2d');
    if (circulationChart) circulationChart.destroy();
    
    circulationChart = new Chart(circulationCtx, {
        type: 'line',
        data: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            datasets: [{
                label: 'Issues',
                data: [120, 150, 180, 200],
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }, {
                label: 'Returns',
                data: [100, 130, 160, 190],
                borderColor: 'rgb(255, 99, 132)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // Collection Distribution Chart
    const collectionCtx = document.getElementById('collectionChart').getContext('2d');
    if (collectionChart) collectionChart.destroy();
    
    collectionChart = new Chart(collectionCtx, {
        type: 'doughnut',
        data: {
            labels: ['Textbooks', 'Fiction', 'Reference', 'Journals', 'E-Books'],
            datasets: [{
                data: [40, 25, 15, 10, 10],
                backgroundColor: [
                    'rgb(255, 99, 132)',
                    'rgb(54, 162, 235)',
                    'rgb(255, 205, 86)',
                    'rgb(75, 192, 192)',
                    'rgb(153, 102, 255)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // Department Usage Chart
    const departmentCtx = document.getElementById('departmentChart').getContext('2d');
    if (departmentChart) departmentChart.destroy();
    
    departmentChart = new Chart(departmentCtx, {
        type: 'bar',
        data: {
            labels: ['Computer Science', 'Mathematics', 'Physics', 'Chemistry', 'Biology'],
            datasets: [{
                label: 'Books Issued',
                data: [300, 250, 200, 180, 150],
                backgroundColor: 'rgba(54, 162, 235, 0.8)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // Peak Hours Chart
    const peakHoursCtx = document.getElementById('peakHoursChart').getContext('2d');
    if (peakHoursChart) peakHoursChart.destroy();
    
    peakHoursChart = new Chart(peakHoursCtx, {
        type: 'bar',
        data: {
            labels: ['9-10', '10-11', '11-12', '12-1', '1-2', '2-3', '3-4', '4-5'],
            datasets: [{
                label: 'Visitors',
                data: [20, 45, 60, 35, 40, 55, 50, 30],
                backgroundColor: 'rgba(255, 206, 86, 0.8)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function loadDetailedTable() {
    const mockData = [
        { metric: 'Total Books Issued', current: '1,250', previous: '1,087', change: '+15%', trend: 'up' },
        { metric: 'Total Books Returned', current: '1,180', previous: '1,050', change: '+12%', trend: 'up' },
        { metric: 'New Registrations', current: '45', previous: '38', change: '+18%', trend: 'up' },
        { metric: 'Overdue Books', current: '85', previous: '92', change: '-8%', trend: 'down' },
        { metric: 'Fine Collected', current: '₹2,500', previous: '₹2,800', change: '-11%', trend: 'down' }
    ];
    
    const tbody = $('#statisticsTableBody');
    tbody.empty();
    
    mockData.forEach(function(row) {
        const trendIcon = row.trend === 'up' ? 'fa-arrow-up text-success' : 'fa-arrow-down text-danger';
        const changeClass = row.trend === 'up' ? 'text-success' : 'text-danger';
        
        const tableRow = `
            <tr>
                <td><strong>${row.metric}</strong></td>
                <td>${row.current}</td>
                <td>${row.previous}</td>
                <td class="${changeClass}">${row.change}</td>
                <td><i class="fas ${trendIcon}"></i></td>
            </tr>
        `;
        tbody.append(tableRow);
    });
}

function exportReport(format) {
    alert('Export functionality would be implemented here for format: ' + format);
}
</script>
{% endblock %}
